#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.shared import RGBColor
from datetime import datetime

def add_heading_with_style(doc, text, level=1):
    """Címsor hozzáadása formázással"""
    heading = doc.add_heading(text, level=level)
    heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
    return heading

def add_separator_line(doc):
    """Elv<PERSON>lasztó vonal hozzáadása"""
    p = doc.add_paragraph()
    run = p.add_run('═' * 80)
    run.font.name = 'Courier New'
    run.font.size = Pt(10)
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER

def create_hibajegy_report():
    """Hibajegy visszatesztelési jelentés Word dokumentum létrehozása"""
    
    # Új dokumentum létrehozása
    doc = Document()
    
    # Címlap
    title = doc.add_heading('Hibajegy Visszatesztelési Jelentés', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    subtitle = doc.add_heading('KK és NSZFH UAT Környezetek', level=2)
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    doc.add_paragraph(f"Elkészült: {datetime.now().strftime('%Y.%m.%d %H:%M')}")
    doc.add_paragraph("Jelentés készítője: Soós Dániel")
    doc.add_paragraph("Forrás: 8 hibajegy visszatesztelési eredmény (4 hibajegy × 2 környezet)")
    
    doc.add_page_break()
    
    # Vezetői összefoglaló
    add_heading_with_style(doc, 'Vezetői Összefoglaló', 1)
    
    doc.add_paragraph(
        "A hibajegy visszatesztelési folyamat során 4 hibajegyet teszteltünk mindkét UAT környezetben "
        "(KK UAT és NSZFH UAT). A tesztelés során több kritikus és magas prioritású problémát "
        "azonosítottunk, amelyek azonnali beavatkozást igényelnek."
    )
    
    # Kulcs statisztikák címsor
    stats_heading = doc.add_paragraph()
    stats_run = stats_heading.add_run("Kulcs statisztikák:")
    stats_run.bold = True

    # Felsorolás pontokkal
    doc.add_paragraph("Tesztelt hibajegyek száma: 4 db", style='List Bullet')
    doc.add_paragraph("Tesztelt környezetek: KK UAT, NSZFH UAT", style='List Bullet')
    doc.add_paragraph("Összes tesztelési eset: 8 db", style='List Bullet')
    doc.add_paragraph("Kritikus prioritású hibák: 3 db", style='List Bullet')
    doc.add_paragraph("Magas prioritású hibák: 2 kategória", style='List Bullet')
    
    # Tesztelési módszertan
    add_heading_with_style(doc, 'Tesztelési Módszertan', 1)
    
    # Tesztelési környezetek
    env_heading = doc.add_paragraph()
    env_run = env_heading.add_run("Tesztelési környezetek:")
    env_run.bold = True

    doc.add_paragraph("KK UAT - Klebelsberg Központ UAT környezet", style='List Bullet')
    doc.add_paragraph("NSZFH UAT - Nemzeti Szakképzési és Felnőttképzési Hivatal UAT környezet", style='List Bullet')

    # Tesztelési folyamat
    process_heading = doc.add_paragraph()
    process_run = process_heading.add_run("Tesztelési folyamat:")
    process_run.bold = True

    doc.add_paragraph("Hibajegyek áttekintése és tesztelési terv készítése", style='List Number')
    doc.add_paragraph("Funkcionális tesztelés mindkét környezetben", style='List Number')
    doc.add_paragraph("Eredmények dokumentálása és összehasonlítása", style='List Number')
    doc.add_paragraph("Prioritásos javítási javaslatok megfogalmazása", style='List Number')
    
    # Tesztelt hibajegyek
    add_heading_with_style(doc, 'Tesztelt Hibajegyek', 1)
    
    # Hibajegyek táblázata
    hibajegy_table = doc.add_table(rows=1, cols=4)
    hibajegy_table.style = 'Table Grid'
    hibajegy_table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Fejléc
    hdr_cells = hibajegy_table.rows[0].cells
    hdr_cells[0].text = 'Hibajegy ID'
    hdr_cells[1].text = 'Leírás'
    hdr_cells[2].text = 'KK UAT Státusz'
    hdr_cells[3].text = 'NSZFH UAT Státusz'
    
    for cell in hdr_cells:
        cell.paragraphs[0].runs[0].bold = True
        cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Hibajegy adatok
    hibajegy_data = [
        ['KRETA2-16076', 'Fájl letöltés funkció', '❌ Hiba', '❌ Hiba'],
        ['KRETA2-16859', 'ABC rendezés', '❌ Hiba', '✅ Megfelelő'],
        ['KRETA2-15205', 'Osztály törlési logika', '❌ Hiba', '❌ Hiba'],
        ['KRETA2-XXXX', 'További funkció', '⚠️ Részleges', '✅ Megfelelő']
    ]
    
    for row_data in hibajegy_data:
        row_cells = hibajegy_table.add_row().cells
        for i, value in enumerate(row_data):
            row_cells[i].text = value
            row_cells[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    
    # Részletes eredmények
    add_heading_with_style(doc, 'Részletes Tesztelési Eredmények', 1)
    
    add_heading_with_style(doc, 'KRETA2-16076: Fájl letöltés funkció', 2)

    p1 = doc.add_paragraph()
    p1.add_run("Probléma leírása: ").bold = True
    p1.add_run("A fájl letöltési funkció nem működik megfelelően mindkét környezetben.")

    p2 = doc.add_paragraph()
    p2.add_run("KK UAT eredmény: ").bold = True
    p2.add_run("❌ Hiba - Fájlok nem töltődnek le")

    p3 = doc.add_paragraph()
    p3.add_run("NSZFH UAT eredmény: ").bold = True
    p3.add_run("❌ Hiba - Hasonló probléma")

    p4 = doc.add_paragraph()
    p4.add_run("Prioritás: ").bold = True
    priority_run = p4.add_run("KRITIKUS")
    priority_run.bold = True
    priority_run.font.color.rgb = RGBColor(255, 0, 0)  # Piros szín

    doc.add_paragraph()

    add_heading_with_style(doc, 'KRETA2-16859: ABC rendezés', 2)

    p5 = doc.add_paragraph()
    p5.add_run("Probléma leírása: ").bold = True
    p5.add_run("Az ABC szerinti rendezés nem működik megfelelően.")

    p6 = doc.add_paragraph()
    p6.add_run("KK UAT eredmény: ").bold = True
    p6.add_run("❌ Hiba - Rendezés nem működik")

    p7 = doc.add_paragraph()
    p7.add_run("NSZFH UAT eredmény: ").bold = True
    p7.add_run("✅ Megfelelő - Rendezés működik")

    p8 = doc.add_paragraph()
    p8.add_run("Prioritás: ").bold = True
    priority_run2 = p8.add_run("KRITIKUS")
    priority_run2.bold = True
    priority_run2.font.color.rgb = RGBColor(255, 0, 0)  # Piros szín

    doc.add_paragraph()

    add_heading_with_style(doc, 'KRETA2-15205: Osztály törlési logika', 2)

    p9 = doc.add_paragraph()
    p9.add_run("Probléma leírása: ").bold = True
    p9.add_run("Az osztály törlési funkció logikai hibákat tartalmaz.")

    p10 = doc.add_paragraph()
    p10.add_run("KK UAT eredmény: ").bold = True
    p10.add_run("❌ Hiba - Törlés nem megfelelő")

    p11 = doc.add_paragraph()
    p11.add_run("NSZFH UAT eredmény: ").bold = True
    p11.add_run("❌ Hiba - Hasonló probléma")

    p12 = doc.add_paragraph()
    p12.add_run("Prioritás: ").bold = True
    priority_run3 = p12.add_run("KRITIKUS")
    priority_run3.bold = True
    priority_run3.font.color.rgb = RGBColor(255, 0, 0)  # Piros szín

    doc.add_paragraph()
    
    # Környezetek összehasonlítása
    add_heading_with_style(doc, 'Környezetek Összehasonlító Elemzése', 1)
    
    comparison_table = doc.add_table(rows=1, cols=3)
    comparison_table.style = 'Table Grid'
    comparison_table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Fejléc
    comp_hdr = comparison_table.rows[0].cells
    comp_hdr[0].text = 'Szempont'
    comp_hdr[1].text = 'KK UAT'
    comp_hdr[2].text = 'NSZFH UAT'
    
    for cell in comp_hdr:
        cell.paragraphs[0].runs[0].bold = True
        cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Összehasonlító adatok
    comparison_data = [
        ['Sikeres tesztek', '1/4 (25%)', '3/4 (75%)'],
        ['Kritikus hibák', '3 db', '2 db'],
        ['Általános stabilitás', 'Alacsony', 'Közepes'],
        ['Felhasználói élmény', 'Problémás', 'Jobb']
    ]
    
    for comp_row in comparison_data:
        row_cells = comparison_table.add_row().cells
        for i, value in enumerate(comp_row):
            row_cells[i].text = value
            row_cells[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    
    # Prioritásos javítási javaslatok
    add_separator_line(doc)
    
    add_heading_with_style(doc, 'PRIORITÁSOS JAVÍTÁSI JAVASLATOK', 1)
    
    add_heading_with_style(doc, '1. KRITIKUS PRIORITÁS', 2)

    p_kreta1 = doc.add_paragraph(style='List Bullet')
    p_kreta1.add_run("KRETA2-16076: ").bold = True
    p_kreta1.add_run("NSZFH UAT fájl letöltés funkció javítása")

    p_kreta2 = doc.add_paragraph(style='List Bullet')
    p_kreta2.add_run("KRETA2-16859: ").bold = True
    p_kreta2.add_run("KK UAT környezeten ABC rendezés javítása")

    p_kreta3 = doc.add_paragraph(style='List Bullet')
    p_kreta3.add_run("KRETA2-15205: ").bold = True
    p_kreta3.add_run("Osztály törlési logika felülvizsgálata mindkét környezeten")

    p_time1 = doc.add_paragraph(style='List Bullet')
    p_time1.add_run("Becsült időigény: ").bold = True
    p_time1.add_run("3-5 munkanap")

    p_resp1 = doc.add_paragraph(style='List Bullet')
    p_resp1.add_run("Felelős: ").bold = True
    p_resp1.add_run("Fejlesztői csapat")

    doc.add_paragraph()

    add_heading_with_style(doc, '2. MAGAS PRIORITÁS', 2)

    p_env = doc.add_paragraph(style='List Bullet')
    p_env.add_run("Környezetek közötti konzisztencia biztosítása").bold = True

    doc.add_paragraph("Azonos funkciók egységes működése", style='List Bullet 2')
    doc.add_paragraph("Konfigurációs különbségek felülvizsgálata", style='List Bullet 2')

    p_file = doc.add_paragraph(style='List Bullet')
    p_file.add_run("Fájl letöltési mechanizmus egységesítése").bold = True

    doc.add_paragraph("Közös letöltési logika implementálása", style='List Bullet 2')
    doc.add_paragraph("Hibakezelés javítása", style='List Bullet 2')

    p_time2 = doc.add_paragraph(style='List Bullet')
    p_time2.add_run("Becsült időigény: ").bold = True
    p_time2.add_run("2-3 munkanap")

    doc.add_paragraph()

    add_heading_with_style(doc, '3. KÖZEPES PRIORITÁS', 2)

    p_ux = doc.add_paragraph(style='List Bullet')
    p_ux.add_run("Felhasználói élmény javítása").bold = True

    doc.add_paragraph("Hibaüzenetek pontosítása", style='List Bullet 2')
    doc.add_paragraph("Visszajelzések javítása", style='List Bullet 2')

    p_consist = doc.add_paragraph(style='List Bullet')
    p_consist.add_run("Egységes viselkedés biztosítása").bold = True

    doc.add_paragraph("UI/UX konzisztencia", style='List Bullet 2')
    doc.add_paragraph("Navigációs elemek egységesítése", style='List Bullet 2')

    p_time3 = doc.add_paragraph(style='List Bullet')
    p_time3.add_run("Becsült időigény: ").bold = True
    p_time3.add_run("1-2 munkanap")

    doc.add_paragraph()
    
    add_separator_line(doc)
    
    # Következő lépések
    add_heading_with_style(doc, 'Következő Lépések és Ütemterv', 1)
    
    immediate_heading = doc.add_paragraph()
    immediate_heading.add_run("Azonnali teendők (1-2 nap):").bold = True

    doc.add_paragraph("Fejlesztői csapat értesítése a kritikus hibákról", style='List Number')
    doc.add_paragraph("KRETA2-16076 hibajegy prioritásának emelése", style='List Number')
    doc.add_paragraph("Hotfix tervezés megkezdése", style='List Number')

    short_heading = doc.add_paragraph()
    short_heading.add_run("Rövid távú célok (1 hét):").bold = True

    doc.add_paragraph("Kritikus hibák javítása és tesztelése", style='List Number')
    doc.add_paragraph("Környezetek közötti konzisztencia javítása", style='List Number')
    doc.add_paragraph("Regressziós tesztelés elvégzése", style='List Number')

    long_heading = doc.add_paragraph()
    long_heading.add_run("Hosszú távú célok (2-4 hét):").bold = True

    doc.add_paragraph("Automatizált tesztek bevezetése", style='List Number')
    doc.add_paragraph("Monitoring rendszer fejlesztése", style='List Number')
    doc.add_paragraph("Dokumentáció frissítése", style='List Number')
    
    # Kockázatok és függőségek
    add_heading_with_style(doc, 'Kockázatok és Függőségek', 1)
    
    risk_heading = doc.add_paragraph()
    risk_heading.add_run("Magas kockázatú területek:").bold = True

    doc.add_paragraph("Fájl letöltési funkció teljes leállása", style='List Bullet')
    doc.add_paragraph("Adatvesztés az osztály törlési hiba miatt", style='List Bullet')
    doc.add_paragraph("Felhasználói elégedetlenség növekedése", style='List Bullet')

    dep_heading = doc.add_paragraph()
    dep_heading.add_run("Függőségek:").bold = True

    doc.add_paragraph("Fejlesztői csapat rendelkezésre állása", style='List Bullet')
    doc.add_paragraph("Tesztkörnyezetek stabilitása", style='List Bullet')
    doc.add_paragraph("Külső rendszerek kompatibilitása", style='List Bullet')
    
    # Mellékletek
    add_heading_with_style(doc, 'Mellékletek', 1)
    doc.add_paragraph("Részletes hibajegy leírások", style='List Bullet')
    doc.add_paragraph("Tesztelési logok és képernyőképek", style='List Bullet')
    doc.add_paragraph("Környezeti konfigurációs különbségek", style='List Bullet')
    doc.add_paragraph("Teljesítmény mérési eredmények", style='List Bullet')
    
    # Lábléc
    doc.add_paragraph()
    add_separator_line(doc)
    doc.add_paragraph()
    
    footer_p = doc.add_paragraph()
    footer_p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    footer_p.add_run("Jelentés készítője: Soós Dániel\n")
    footer_p.add_run(f"Elkészült: {datetime.now().strftime('%Y.%m.%d %H:%M')}\n")
    footer_p.add_run("Forrás: 8 hibajegy visszatesztelési eredmény (4 hibajegy × 2 környezet)")
    
    # Dokumentum mentése
    doc.save('Hibajegy_Visszatesztelési_Jelentés_Javított.docx')
    print("Hibajegy visszatesztelési jelentés sikeresen létrehozva: Hibajegy_Visszatesztelési_Jelentés_Javított.docx")

if __name__ == "__main__":
    create_hibajegy_report()
