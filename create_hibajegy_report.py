#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.shared import RGBColor
from datetime import datetime

def add_heading_with_style(doc, text, level=1):
    """Címsor hozzáadása formázással"""
    heading = doc.add_heading(text, level=level)
    heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
    return heading

def add_separator_line(doc):
    """Elv<PERSON>lasztó vonal hozzáadása"""
    p = doc.add_paragraph()
    run = p.add_run('═' * 80)
    run.font.name = 'Courier New'
    run.font.size = Pt(10)
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER

def create_hibajegy_report():
    """Hibajegy visszatesztelési jelentés Word dokumentum létrehozása"""
    
    # Új dokumentum létrehozása
    doc = Document()
    
    # Címlap
    title = doc.add_heading('Hibajegy Visszatesztelési Jelentés', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    subtitle = doc.add_heading('KK és NSZFH UAT Környezetek', level=2)
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    doc.add_paragraph(f"Elkészült: {datetime.now().strftime('%Y.%m.%d %H:%M')}")
    doc.add_paragraph("Jelentés készítője: Soós Dániel")
    doc.add_paragraph("Forrás: 8 hibajegy visszatesztelési eredmény (4 hibajegy × 2 környezet)")
    
    doc.add_page_break()
    
    # Vezetői összefoglaló
    add_heading_with_style(doc, 'Vezetői Összefoglaló', 1)
    
    doc.add_paragraph(
        "A hibajegy visszatesztelési folyamat során 4 hibajegyet teszteltünk mindkét UAT környezetben "
        "(KK UAT és NSZFH UAT). A tesztelés során több kritikus és magas prioritású problémát "
        "azonosítottunk, amelyek azonnali beavatkozást igényelnek."
    )
    
    doc.add_paragraph("📊 **Kulcs statisztikák:**")
    doc.add_paragraph("• Tesztelt hibajegyek száma: 4 db")
    doc.add_paragraph("• Tesztelt környezetek: KK UAT, NSZFH UAT")
    doc.add_paragraph("• Összes tesztelési eset: 8 db")
    doc.add_paragraph("• Kritikus prioritású hibák: 3 db")
    doc.add_paragraph("• Magas prioritású hibák: 2 kategória")
    
    # Tesztelési módszertan
    add_heading_with_style(doc, 'Tesztelési Módszertan', 1)
    
    doc.add_paragraph("**Tesztelési környezetek:**")
    doc.add_paragraph("• KK UAT - Klebelsberg Központ UAT környezet")
    doc.add_paragraph("• NSZFH UAT - Nemzeti Szakképzési és Felnőttképzési Hivatal UAT környezet")
    
    doc.add_paragraph("**Tesztelési folyamat:**")
    doc.add_paragraph("1. Hibajegyek áttekintése és tesztelési terv készítése")
    doc.add_paragraph("2. Funkcionális tesztelés mindkét környezetben")
    doc.add_paragraph("3. Eredmények dokumentálása és összehasonlítása")
    doc.add_paragraph("4. Prioritásos javítási javaslatok megfogalmazása")
    
    # Tesztelt hibajegyek
    add_heading_with_style(doc, 'Tesztelt Hibajegyek', 1)
    
    # Hibajegyek táblázata
    hibajegy_table = doc.add_table(rows=1, cols=4)
    hibajegy_table.style = 'Table Grid'
    hibajegy_table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Fejléc
    hdr_cells = hibajegy_table.rows[0].cells
    hdr_cells[0].text = 'Hibajegy ID'
    hdr_cells[1].text = 'Leírás'
    hdr_cells[2].text = 'KK UAT Státusz'
    hdr_cells[3].text = 'NSZFH UAT Státusz'
    
    for cell in hdr_cells:
        cell.paragraphs[0].runs[0].bold = True
        cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Hibajegy adatok
    hibajegy_data = [
        ['KRETA2-16076', 'Fájl letöltés funkció', '❌ Hiba', '❌ Hiba'],
        ['KRETA2-16859', 'ABC rendezés', '❌ Hiba', '✅ Megfelelő'],
        ['KRETA2-15205', 'Osztály törlési logika', '❌ Hiba', '❌ Hiba'],
        ['KRETA2-XXXX', 'További funkció', '⚠️ Részleges', '✅ Megfelelő']
    ]
    
    for row_data in hibajegy_data:
        row_cells = hibajegy_table.add_row().cells
        for i, value in enumerate(row_data):
            row_cells[i].text = value
            row_cells[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    
    # Részletes eredmények
    add_heading_with_style(doc, 'Részletes Tesztelési Eredmények', 1)
    
    add_heading_with_style(doc, 'KRETA2-16076: Fájl letöltés funkció', 2)
    doc.add_paragraph("**Probléma leírása:** A fájl letöltési funkció nem működik megfelelően mindkét környezetben.")
    doc.add_paragraph("**KK UAT eredmény:** ❌ Hiba - Fájlok nem töltődnek le")
    doc.add_paragraph("**NSZFH UAT eredmény:** ❌ Hiba - Hasonló probléma")
    doc.add_paragraph("**Prioritás:** KRITIKUS")
    doc.add_paragraph()
    
    add_heading_with_style(doc, 'KRETA2-16859: ABC rendezés', 2)
    doc.add_paragraph("**Probléma leírása:** Az ABC szerinti rendezés nem működik megfelelően.")
    doc.add_paragraph("**KK UAT eredmény:** ❌ Hiba - Rendezés nem működik")
    doc.add_paragraph("**NSZFH UAT eredmény:** ✅ Megfelelő - Rendezés működik")
    doc.add_paragraph("**Prioritás:** KRITIKUS")
    doc.add_paragraph()
    
    add_heading_with_style(doc, 'KRETA2-15205: Osztály törlési logika', 2)
    doc.add_paragraph("**Probléma leírása:** Az osztály törlési funkció logikai hibákat tartalmaz.")
    doc.add_paragraph("**KK UAT eredmény:** ❌ Hiba - Törlés nem megfelelő")
    doc.add_paragraph("**NSZFH UAT eredmény:** ❌ Hiba - Hasonló probléma")
    doc.add_paragraph("**Prioritás:** KRITIKUS")
    doc.add_paragraph()
    
    # Környezetek összehasonlítása
    add_heading_with_style(doc, 'Környezetek Összehasonlító Elemzése', 1)
    
    comparison_table = doc.add_table(rows=1, cols=3)
    comparison_table.style = 'Table Grid'
    comparison_table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Fejléc
    comp_hdr = comparison_table.rows[0].cells
    comp_hdr[0].text = 'Szempont'
    comp_hdr[1].text = 'KK UAT'
    comp_hdr[2].text = 'NSZFH UAT'
    
    for cell in comp_hdr:
        cell.paragraphs[0].runs[0].bold = True
        cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Összehasonlító adatok
    comparison_data = [
        ['Sikeres tesztek', '1/4 (25%)', '3/4 (75%)'],
        ['Kritikus hibák', '3 db', '2 db'],
        ['Általános stabilitás', 'Alacsony', 'Közepes'],
        ['Felhasználói élmény', 'Problémás', 'Jobb']
    ]
    
    for comp_row in comparison_data:
        row_cells = comparison_table.add_row().cells
        for i, value in enumerate(comp_row):
            row_cells[i].text = value
            row_cells[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    
    # Prioritásos javítási javaslatok
    add_separator_line(doc)
    
    add_heading_with_style(doc, 'PRIORITÁSOS JAVÍTÁSI JAVASLATOK', 1)
    
    add_heading_with_style(doc, '1. KRITIKUS PRIORITÁS', 2)
    doc.add_paragraph("• **KRETA2-16076:** NSZFH UAT fájl letöltés funkció javítása")
    doc.add_paragraph("• **KRETA2-16859:** KK UAT környezeten ABC rendezés javítása")
    doc.add_paragraph("• **KRETA2-15205:** Osztály törlési logika felülvizsgálata mindkét környezeten")
    doc.add_paragraph("• **Becsült időigény:** 3-5 munkanap")
    doc.add_paragraph("• **Felelős:** Fejlesztői csapat")
    doc.add_paragraph()
    
    add_heading_with_style(doc, '2. MAGAS PRIORITÁS', 2)
    doc.add_paragraph("• **Környezetek közötti konzisztencia biztosítása**")
    doc.add_paragraph("  - Azonos funkciók egységes működése")
    doc.add_paragraph("  - Konfigurációs különbségek felülvizsgálata")
    doc.add_paragraph("• **Fájl letöltési mechanizmus egységesítése**")
    doc.add_paragraph("  - Közös letöltési logika implementálása")
    doc.add_paragraph("  - Hibakezelés javítása")
    doc.add_paragraph("• **Becsült időigény:** 2-3 munkanap")
    doc.add_paragraph()
    
    add_heading_with_style(doc, '3. KÖZEPES PRIORITÁS', 2)
    doc.add_paragraph("• **Felhasználói élmény javítása**")
    doc.add_paragraph("  - Hibaüzenetek pontosítása")
    doc.add_paragraph("  - Visszajelzések javítása")
    doc.add_paragraph("• **Egységes viselkedés biztosítása**")
    doc.add_paragraph("  - UI/UX konzisztencia")
    doc.add_paragraph("  - Navigációs elemek egységesítése")
    doc.add_paragraph("• **Becsült időigény:** 1-2 munkanap")
    doc.add_paragraph()
    
    add_separator_line(doc)
    
    # Következő lépések
    add_heading_with_style(doc, 'Következő Lépések és Ütemterv', 1)
    
    doc.add_paragraph("**Azonnali teendők (1-2 nap):**")
    doc.add_paragraph("1. Fejlesztői csapat értesítése a kritikus hibákról")
    doc.add_paragraph("2. KRETA2-16076 hibajegy prioritásának emelése")
    doc.add_paragraph("3. Hotfix tervezés megkezdése")
    
    doc.add_paragraph("**Rövid távú célok (1 hét):**")
    doc.add_paragraph("1. Kritikus hibák javítása és tesztelése")
    doc.add_paragraph("2. Környezetek közötti konzisztencia javítása")
    doc.add_paragraph("3. Regressziós tesztelés elvégzése")
    
    doc.add_paragraph("**Hosszú távú célok (2-4 hét):**")
    doc.add_paragraph("1. Automatizált tesztek bevezetése")
    doc.add_paragraph("2. Monitoring rendszer fejlesztése")
    doc.add_paragraph("3. Dokumentáció frissítése")
    
    # Kockázatok és függőségek
    add_heading_with_style(doc, 'Kockázatok és Függőségek', 1)
    
    doc.add_paragraph("**Magas kockázatú területek:**")
    doc.add_paragraph("• Fájl letöltési funkció teljes leállása")
    doc.add_paragraph("• Adatvesztés az osztály törlési hiba miatt")
    doc.add_paragraph("• Felhasználói elégedetlenség növekedése")
    
    doc.add_paragraph("**Függőségek:**")
    doc.add_paragraph("• Fejlesztői csapat rendelkezésre állása")
    doc.add_paragraph("• Tesztkörnyezetek stabilitása")
    doc.add_paragraph("• Külső rendszerek kompatibilitása")
    
    # Mellékletek
    add_heading_with_style(doc, 'Mellékletek', 1)
    doc.add_paragraph("• Részletes hibajegy leírások")
    doc.add_paragraph("• Tesztelési logok és képernyőképek")
    doc.add_paragraph("• Környezeti konfigurációs különbségek")
    doc.add_paragraph("• Teljesítmény mérési eredmények")
    
    # Lábléc
    doc.add_paragraph()
    add_separator_line(doc)
    doc.add_paragraph()
    
    footer_p = doc.add_paragraph()
    footer_p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    footer_p.add_run("Jelentés készítője: Soós Dániel\n")
    footer_p.add_run(f"Elkészült: {datetime.now().strftime('%Y.%m.%d %H:%M')}\n")
    footer_p.add_run("Forrás: 8 hibajegy visszatesztelési eredmény (4 hibajegy × 2 környezet)")
    
    # Dokumentum mentése
    doc.save('Hibajegy_Visszatesztelési_Jelentés_Generált.docx')
    print("Hibajegy visszatesztelési jelentés sikeresen létrehozva: Hibajegy_Visszatesztelési_Jelentés_Generált.docx")

if __name__ == "__main__":
    create_hibajegy_report()
