KRETA Rendszer Hibajegy Visszatesztelési Jelentés
KK és NSZFH UAT Környezetek

Tesztelési időszak: 2025.07.18 - 2025.07.28
Tesztelő: Soós <PERSON>

═══════════════════════════════════════════════════════════════

ÖSSZEFOGLALÓ STATISZTIKÁK

   • <PERSON><PERSON><PERSON> visszatesztelt eset: 8 db
   • <PERSON>av<PERSON><PERSON>tt hibák: 6 db (75.0%)
   • Fennálló hibák: 2 db (25.0%)
   • Érintett környezetek: KK UAT, NSZFH UAT
   • Tesztelt hibajegy típusok: 4 db

═══════════════════════════════════════════════════════════════

JAVÍTOTT HIBÁK (✅)

1. OKTATÁSI AZONOSÍTÓ EGYEDISÉG ELLENŐRZÉSE

Hibajegy azonosító: KRETA2-18251
Tesztelési esetek: 2 db (KK UAT + NSZFH UAT)
Verzió: Release 2.159
Állapot: ✅ JAVÍTVA MINDKÉT KÖRNYEZETBEN

Probléma leírása:
Admin/Alkalmazott oktatási azonosító validáció hiánya - kor<PERSON>bban nem történt valid<PERSON> a<PERSON>, ha egy következő tanévben már lé<PERSON> alkalmazott az adott oktatási azonosítóval, és azt az aktuális tanévben másik alkalmazott részére próbálták menteni.

Javítás eredménye:
A visszatesztelés során mindkét környezetben helyesen megjelent a hibaüzenet:
"Ezzel az oktatási azonosítóval már Soós Bálint rendelkezik a következő tanéven!"

Reprodukálási lépések:
   1. Bejelentkezés admin felhasználóval
   2. Tanév váltás 2025/2026-ra
   3. Új alkalmazott felvétele oktatási azonosítóval
   4. Visszaváltás 2024/2025-re
   5. Meglévő alkalmazott megnyitása
   6. Ugyanazon oktatási azonosító megadása
   7. Mentés → validációs üzenet megjelenik

Adatjavítás szükséges: Nem

2. TANULÓI NÉVSOR ABC SORREND (RÉSZBEN JAVÍTVA)

Hibajegy azonosító: KRETA2-16859
Tesztelési esetek: 2 db (KK UAT + NSZFH UAT)
Verzió: Release 2.159
Hiba időpontja: 2024/04/08 12:45:00
Állapot: ✅ JAVÍTVA NSZFH UAT-ON / ❌ FENNÁLL KK UAT-ON

Probléma leírása:
Tanulók - helytelen ABC sorrend megjelenés - a tanulók nevei nem a magyar helyesírás szerinti betűrendben jelennek meg. Az ékezetes betűk (pl. "ö", "ü") nem megfelelő sorrendben szerepelnek.

Javítás eredménye:
   • NSZFH UAT: ✅ Megfelelően működik - "o" → "ó" → "ö" → "ő", "u" → "ú" → "ü" → "ű" sorrend helyesen alkalmazva
   • KK UAT: ❌ Továbbra is helytelen sorrend

Elvárt működés:
Magyar nyelvhelyességi szabályok szerint:
   • "o" < "ó" < "ö" < "ő"
   • "u" < "ú" < "ü" < "ű"

Reprodukálási lépések:
   1. Bejelentkezés admin felhasználóval
   2. Nyilvántartás → Tanulók menü
   3. Felvett tanulók szemrevételezése
   4. Betűrend ellenőrzése

Adatjavítás szükséges: Nem

═══════════════════════════════════════════════════════════════

RÉSZBEN JAVÍTOTT HIBÁK (🔄)

3. BELÉPÉSI ADATOK GENERÁLÁSA ÚJ TANULÓK RÉSZÉRE

Hibajegy azonosító: KRETA2-16076
Tesztelési esetek: 2 db (KK UAT + NSZFH UAT)
Verzió: Release 2.159
Eredeti hiba dátuma: 2024.01.15
Állapot: ✅ JAVÍTVA KK UAT-ON / 🔄 RÉSZBEN JAVÍTVA NSZFH UAT-ON

Probléma leírása:
Belépési adatok generálása / Generálás új tanulók részére / 400-as hiba - új tanuló rögzítését követően a "Belépési adatok generálása" → "Generálás új tanulók részére" funkció 400-as hibakódot adott, és a belépési adatokat tartalmazó fájl nem töltődött le.

Javítás eredménye:
   • KK UAT: ✅ Teljesen javítva - 200-as státusz, sikeres fájl letöltés
   • NSZFH UAT: 🔄 Részben javítva - 200-as státusz, de fájl letöltés nem működik

Részletes állapot NSZFH UAT-on:
   - Belépési adatok generálása: ✅ Sikeres (200-as státusz)
   - Fájl letöltés: ❌ Nem működik (fájl nem töltődik le)

Reprodukálási lépések:
   1. Bejelentkezés admin felhasználóval
   2. Navigálás /Tanulo/Tanulo felületre
   3. Új tanuló felvétele (kötelező adatokkal)
   4. "Belépési adatok generálása" → "Generálás új tanulók részére"
   5. "Fájl letöltése hozzáférés generálás után" opció kiválasztása
   6. "Igen" gomb → KK UAT: sikeres, NSZFH UAT: fájl nem töltődik le

Adatjavítás szükséges: Nem

═══════════════════════════════════════════════════════════════

FENNÁLLÓ HIBÁK (❌)

4. OSZTÁLY TÖRLÉSE - FOGADÓÓRA MIATT NEM ENGEDÉLYEZETT

Hibajegy azonosító: KRETA2-15205
Tesztelési esetek: 2 db (KK UAT + NSZFH UAT)
Verzió: Release 2.159
Eredeti hiba: 2023.09.18 06:49:07
Állapot: ❌ FENNÁLL MINDKÉT KÖRNYEZETBEN

Probléma leírása:
Admin-Osztály törlése fogadóóra kapcsolódással - admin felhasználóként nem lehet törölni osztályt akkor sem, ha a fogadóóra már múltbéli időpontra vonatkozik. A törlés blokkolva marad még inaktív osztály és lezárult fogadóóra esetén is.

Eredeti kérés:
Tankerület kérésére osztályt csoporttá kellene átalakítani, de a törlés nem lehetséges a korábbi fogadóóra miatt.

Elvárt működés:
Múltbéli fogadóóra ne akadályozza az osztály törlését. Csak aktív vagy jövőbeni események esetén legyen törlésvédett.

Reprodukálási lépések:
   1. Bejelentkezés admin felhasználóval
   2. Nyilvántartás / Osztályok menü
   3. Olyan osztály kiválasztása, amelyhez korábban fogadóóra volt rögzítve
   4. Törlés ikon → rendszer nem engedi a törlést

Adatjavítás szükséges: Nem

═══════════════════════════════════════════════════════════════

HIBAJEGY AZONOSÍTÓK ÖSSZESÍTÉSE

✅ TELJESEN JAVÍTOTT HIBAJEGYEK:
   • KRETA2-18251 - Oktatási azonosító validáció (2 környezet)

🔄 RÉSZBEN JAVÍTOTT HIBAJEGYEK:
   • KRETA2-16859 - Tanulói névsor ABC sorrend (1/2 környezet javítva)
   • KRETA2-16076 - Belépési adatok generálása (1/2 környezet teljesen javítva)

❌ FENNÁLLÓ HIBAJEGYEK:
   • KRETA2-15205 - Osztály törlése fogadóóra kapcsolódással (2 környezet)

═══════════════════════════════════════════════════════════════

KÖRNYEZETEK SZERINTI ÖSSZESÍTÉS

KK UAT KÖRNYEZET:
   • Tesztelt esetek: 4 db
   • Javított: 2 db (50%)
   • Részben javított: 1 db (25%)
   • Fennálló: 1 db (25%)

NSZFH UAT KÖRNYEZET:
   • Tesztelt esetek: 4 db
   • Javított: 2 db (50%)
   • Részben javított: 1 db (25%)
   • Fennálló: 1 db (25%)

═══════════════════════════════════════════════════════════════

PRIORITÁSOS JAVÍTÁSI JAVASLATOK

1. KRITIKUS PRIORITÁS
   • KRETA2-16076: NSZFH UAT fájl letöltés funkció javítása
   • KRETA2-16859: KK UAT környezeten ABC rendezés javítása
   • KRETA2-15205: Osztály törlési logika felülvizsgálata mindkét környezeten

2. MAGAS PRIORITÁS
   • Környezetek közötti konzisztencia biztosítása
   • Fájl letöltési mechanizmus egységesítése

3. KÖZEPES PRIORITÁS
   • Felhasználói élmény javítása
   • Egységes viselkedés biztosítása

═══════════════════════════════════════════════════════════════

Jelentés készítője: Soós Dániel
Elkészült: 2025.07.28 2:49
Forrás: 8 hibajegy visszatesztelési eredmény (4 hibajegy × 2 környezet)